from flask import Flask, render_template, jsonify, request
import random

app = Flask(__name__)

# Global temperature variable
temp = 20.0  # Default temperature

def get_pencil_color(temperature):
    """Return color based on temperature value"""
    if temperature < 0:
        return "#0066CC"  # Blue for very cold
    elif temperature < 10:
        return "#00AAFF"  # Light blue for cold
    elif temperature < 20:
        return "#00FF00"  # Green for cool
    elif temperature < 30:
        return "#FFFF00"  # Yellow for warm
    elif temperature < 40:
        return "#FF8800"  # Orange for hot
    else:
        return "#FF0000"  # Red for very hot

@app.route('/')
def index():
    return render_template('index.html', temp=temp, color=get_pencil_color(temp))

@app.route('/generate_random')
def generate_random():
    global temp
    temp = round(random.uniform(-10, 50), 1)  # Random temp between -10 and 50
    return jsonify({'temp': temp, 'color': get_pencil_color(temp)})

@app.route('/read_temp')
def read_temp():
    return jsonify({'temp': temp, 'color': get_pencil_color(temp)})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)