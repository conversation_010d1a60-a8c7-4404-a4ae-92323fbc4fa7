from flask import Flask, render_template, jsonify, request
import random

app = Flask(__name__)

# Global temperature variable
temp = 22.0  # Default temperature (in cyan range)

def get_pencil_color(temperature):
    """Return color based on temperature value"""
    if 20 <= temperature <= 24:
        return "#00FFFF"  # Cyan for 20-24°C
    elif 25 <= temperature <= 28:
        return "#FFFF00"  # Yellow for 25-28°C
    elif 29 <= temperature <= 33:
        return "#FF0000"  # Red for 29-33°C
    else:
        return "#808080"  # Gray for temperatures outside the specified ranges

@app.route('/')
def index():
    return render_template('index.html', temp=temp, color=get_pencil_color(temp))

@app.route('/generate_random')
def generate_random():
    global temp
    temp = round(random.uniform(18, 35), 1)  # Random temp between 18 and 35 to cover the ranges
    return jsonify({'temp': temp, 'color': get_pencil_color(temp)})

@app.route('/read_temp')
def read_temp():
    return jsonify({'temp': temp, 'color': get_pencil_color(temp)})

if __name__ == '__main__':
    app.run(debug=True, host='0.0.0.0', port=5000)