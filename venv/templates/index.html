<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Temperature Pencil</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            margin: 0;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
        }
        
        .container {
            text-align: center;
            background: rgba(255, 255, 255, 0.1);
            padding: 40px;
            border-radius: 20px;
            backdrop-filter: blur(10px);
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        
        h1 {
            margin-bottom: 30px;
            font-size: 2.5em;
            text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.3);
        }
        
        .pencil-container {
            margin: 30px 0;
        }
        
        .pencil {
            width: 300px;
            height: 20px;
            background: linear-gradient(to right, #8B4513 0%, #8B4513 80%, var(--pencil-color) 80%, var(--pencil-color) 100%);
            border-radius: 10px;
            margin: 20px auto;
            position: relative;
            box-shadow: 0 4px 8px rgba(0, 0, 0, 0.3);
            transition: all 0.3s ease;
        }
        
        .pencil::before {
            content: '';
            position: absolute;
            right: -10px;
            top: 50%;
            transform: translateY(-50%);
            width: 0;
            height: 0;
            border-left: 10px solid var(--pencil-color);
            border-top: 10px solid transparent;
            border-bottom: 10px solid transparent;
        }
        
        .pencil::after {
            content: '';
            position: absolute;
            left: -5px;
            top: 50%;
            transform: translateY(-50%);
            width: 10px;
            height: 10px;
            background: #FFD700;
            border-radius: 50%;
        }
        
        .temp-display {
            font-size: 2em;
            margin: 20px 0;
            padding: 15px;
            background: rgba(255, 255, 255, 0.2);
            border-radius: 10px;
            display: inline-block;
            min-width: 200px;
        }
        
        .buttons {
            margin: 30px 0;
        }
        
        button {
            background: linear-gradient(45deg, #FF6B6B, #4ECDC4);
            border: none;
            color: white;
            padding: 15px 30px;
            margin: 0 10px;
            border-radius: 25px;
            cursor: pointer;
            font-size: 1.1em;
            font-weight: bold;
            transition: all 0.3s ease;
            box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
        }
        
        button:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0, 0, 0, 0.3);
        }
        
        button:active {
            transform: translateY(0);
        }
        
        .color-info {
            margin-top: 20px;
            font-size: 1.1em;
            opacity: 0.9;
        }
        
        .loading {
            opacity: 0.6;
            pointer-events: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🌡️ Temperature Pencil</h1>
        
        <div class="pencil-container">
            <div class="pencil" id="pencil" style="--pencil-color: {{ color }}"></div>
        </div>
        
        <div class="temp-display" id="tempDisplay">
            {{ temp }}°C
        </div>

        <div class="stored-temp" id="storedTemp" style="font-size: 1.2em; margin: 10px 0; padding: 10px; background: rgba(255, 255, 255, 0.1); border-radius: 8px; display: inline-block;">
            Stored temp: {{ temp }}°C
        </div>
        
        <div class="buttons">
            <button onclick="generateRandom()" id="randomBtn">🎲 Generate Random Temp</button>
            <button onclick="readTemp()" id="readBtn">📖 Read Current Temp & Update Pencil</button>
        </div>
        
        <div class="color-info" id="colorInfo">
            Current color represents the temperature
        </div>

        <div class="color-legend" style="margin-top: 20px; font-size: 0.9em; opacity: 0.8;">
            <div style="margin: 5px 0;"><span style="color: #00FFFF;">●</span> Cyan: 20-24°C</div>
            <div style="margin: 5px 0;"><span style="color: #FFFF00;">●</span> Yellow: 25-28°C</div>
            <div style="margin: 5px 0;"><span style="color: #FF0000;">●</span> Red: 29-33°C</div>
            <div style="margin: 5px 0;"><span style="color: #808080;">●</span> Gray: Outside range</div>
        </div>
    </div>

    <script>
        function updatePencil(temp, color) {
            const pencil = document.getElementById('pencil');
            const tempDisplay = document.getElementById('tempDisplay');
            
            pencil.style.setProperty('--pencil-color', color);
            tempDisplay.textContent = temp + '°C';
            
            // Update color info
            const colorInfo = document.getElementById('colorInfo');
            let tempRange = '';
            if (temp >= 20 && temp <= 24) tempRange = 'Cyan Range (20-24°C)';
            else if (temp >= 25 && temp <= 28) tempRange = 'Yellow Range (25-28°C)';
            else if (temp >= 29 && temp <= 33) tempRange = 'Red Range (29-33°C)';
            else tempRange = 'Outside Range (Gray)';

            colorInfo.textContent = `${tempRange} - Color: ${color}`;
        }
        
        function generateRandom() {
            const container = document.querySelector('.container');
            container.classList.add('loading');

            fetch('/generate_random')
                .then(response => response.json())
                .then(data => {
                    // Update stored temperature display but don't update pencil
                    const storedTemp = document.getElementById('storedTemp');
                    storedTemp.textContent = `Stored temp: ${data.temp}°C`;

                    const colorInfo = document.getElementById('colorInfo');
                    colorInfo.textContent = `New temperature generated: ${data.temp}°C (Click "Read Current Temp" to update pencil)`;
                    container.classList.remove('loading');
                })
                .catch(error => {
                    console.error('Error:', error);
                    container.classList.remove('loading');
                });
        }
        
        function readTemp() {
            const container = document.querySelector('.container');
            container.classList.add('loading');
            
            fetch('/read_temp')
                .then(response => response.json())
                .then(data => {
                    updatePencil(data.temp, data.color);
                    container.classList.remove('loading');
                })
                .catch(error => {
                    console.error('Error:', error);
                    container.classList.remove('loading');
                });
        }
        
        // Initialize with current values
        updatePencil({{ temp }}, '{{ color }}');
    </script>
</body>
</html>
